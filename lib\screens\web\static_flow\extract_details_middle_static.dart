import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/models/object_creation_model.dart';
import 'package:nsl/screens/web/static_flow/components/manual_processing_component.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'dart:math' as math;

import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractDetailsMiddleStatic extends StatefulWidget {
  final String? jobId;
  final String? userIntent;

  const ExtractDetailsMiddleStatic({
    super.key,
    this.jobId,
    this.userIntent,
  });

  @override
  State<ExtractDetailsMiddleStatic> createState() =>
      _ExtractDetailsMiddleStaticState();
}

class _ExtractDetailsMiddleStaticState
    extends State<ExtractDetailsMiddleStatic> {
  late AccordionController _accordionController;

  // Shared scroll controller for synchronized vertical scrolling
  final ScrollController _verticalController = ScrollController();
  final ScrollController _actionsController = ScrollController();

  // Data storage for tables
  List<Map<String, String>> _attributeData = [
    {
      'name': 'customer_id',
      'displayName': 'Customer ID',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES',
      'defaultType': 'N/A',
      'defaultValue': 'N/A',
      'description': 'Unique identifier for customer records',
      'helperText': 'Enter customer ID',
      'enumValues': 'N/A',
      'validation': 'Required',
    },
    {
      'name': 'email',
      'displayName': 'Email Address',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES',
      'defaultType': 'N/A',
      'defaultValue': 'N/A',
      'description': 'Primary email address for customer communication',
      'helperText': 'Enter email address',
      'enumValues': 'N/A',
      'validation': 'Required',
    },
    {
      'name': 'name',
      'displayName': 'Full Name',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'NO',
      'defaultType': 'N/A',
      'defaultValue': 'N/A',
      'description': 'Complete name of the customer',
      'helperText': 'Enter full name',
      'enumValues': 'N/A',
      'validation': 'Required',
    },
  ];

  Map<String, List<Map<String, String>>> _tableData = {};

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });

    // Set up bidirectional scroll synchronization
    _verticalController.addListener(_syncScrollControllers);
    _actionsController.addListener(_syncFromActionsController);

    // Load API data on initialization
    _loadApiData();
  }

  void _syncScrollControllers() {
    if (_verticalController.hasClients && _actionsController.hasClients) {
      // Sync actions controller to match vertical controller position
      if (_actionsController.offset != _verticalController.offset) {
        _actionsController.jumpTo(_verticalController.offset);
      }
    }
  }

  void _syncFromActionsController() {
    if (_verticalController.hasClients && _actionsController.hasClients) {
      // Sync vertical controller to match actions controller position
      if (_verticalController.offset != _actionsController.offset) {
        _verticalController.jumpTo(_actionsController.offset);
      }
    }
  }

  /// Get default user intent for testing if none provided
  // String get _defaultUserIntent =>
  //     "I need to build a comprehensive CRM system. I need Contact entities with personal information like names, email addresses, phone numbers, job titles, and company affiliations. Each Contact can have multiple communication preferences, social media profiles, and interaction history. Contacts will have lead sources, qualification status, and engagement scoring.";

  /// Load data from the API using provider
  Future<void> _loadApiData() async {
    final objectCreationProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    bool success = false;

    try {
      // Check if we have a userIntent to run the complete workflow
      if (widget.userIntent != null && widget.userIntent!.isNotEmpty) {
        // Use the complete workflow: create job, wait for completion, then fetch entities
        success =
            await objectCreationProvider.executeCompleteExtractionWorkflow(
          userIntent: widget.userIntent!,
        );
      }
      // Check if we have a jobId to fetch entities directly
      else if (widget.jobId != null && widget.jobId!.isNotEmpty) {
        // Use direct entity fetching for existing job
        success = await objectCreationProvider.getJobEntities(widget.jobId!);
      }
      // No jobId or userIntent provided - use default user intent
      else {
        // Use the complete workflow with default user intent
        // success =
        //     await objectCreationProvider.executeCompleteExtractionWorkflow(
        //   userIntent: _defaultUserIntent,
        // );
      }

      if (success) {
        print(
            'Successfully loaded ${objectCreationProvider.objects.length} objects from API');
      } else {
        print('API Error: ${objectCreationProvider.error}');
      }
    } catch (e) {
      print('Exception in _loadApiData: $e');
    }
  }

  @override
  void dispose() {
    _verticalController.removeListener(_syncScrollControllers);
    _verticalController.dispose();
    _actionsController.dispose();
    _accordionController.dispose();
    super.dispose();
  }

  /// Helper method to get status text based on count
  String _getStatusFromCount(int count) {
    if (count == 0) {
      return 'Missing';
    } else if (count > 0 && count < 5) {
      return 'Partial Completion';
    } else {
      return 'Completed';
    }
  }

  /// Helper method to get background color based on count
  Color _getBackgroundColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFFFEE2E2); // Red background for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFFFEF3C7); // Yellow background for partial
    } else {
      return const Color(0xFFD1FAE5); // Green background for completed
    }
  }

  /// Helper method to get text color based on count
  Color _getTextColorFromCount(int count) {
    if (count == 0) {
      return const Color(0xFF991B1B); // Red text for missing
    } else if (count > 0 && count < 5) {
      return const Color(0xFF92400E); // Yellow text for partial
    } else {
      return const Color(0xFF065F46); // Green text for completed
    }
  }

  /// Helper method to convert API attributes to table data format
  List<Map<String, String>> _getAttributeTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.attributes == null || objectData!.attributes!.isEmpty) {
      return _getEnhancedStaticAttributeData(); // Return enhanced static data as fallback
    }

    return objectData.attributes!.map((attribute) {
      return {
        'name': attribute.name ?? 'Unknown',
        'displayName': attribute.displayName ?? attribute.name ?? 'Unknown',
        'dataType': attribute.dataType ?? 'string',
        'required': (attribute.required ?? false) ? 'YES' : 'NO',
        'unique': (attribute.unique ?? false) ? 'YES' : 'NO',
        'defaultType': attribute.defaultType?.isNotEmpty == true
            ? attribute.defaultType!
            : 'N/A',
        'defaultValue': attribute.defaultValue?.isNotEmpty == true
            ? attribute.defaultValue!
            : 'N/A',
        'description': attribute.description?.isNotEmpty == true
            ? attribute.description!
            : 'N/A',
        'helperText': attribute.helperText?.isNotEmpty == true
            ? attribute.helperText!
            : 'N/A',
        'enumValues': attribute.enumValues?.isNotEmpty == true
            ? attribute.enumValues!.join(', ')
            : 'N/A',
        'validation':
            attribute.validation?.required == true ? 'Required' : 'Optional',
      };
    }).toList();
  }

  /// Helper method to get enhanced static attribute data with new fields
  List<Map<String, String>> _getEnhancedStaticAttributeData() {
    return [
      {
        'name': 'customer_id',
        'displayName': 'Customer ID',
        'dataType': 'string',
        'required': 'YES',
        'unique': 'YES',
        'defaultType': 'N/A',
        'defaultValue': 'N/A',
        'description': 'Unique identifier for customer records',
        'helperText': 'Enter customer ID',
        'enumValues': 'N/A',
        'validation': 'Required',
      },
      {
        'name': 'email',
        'displayName': 'Email Address',
        'dataType': 'string',
        'required': 'YES',
        'unique': 'YES',
        'defaultType': 'N/A',
        'defaultValue': 'N/A',
        'description': 'Primary email address for customer communication',
        'helperText': 'Enter email address',
        'enumValues': 'N/A',
        'validation': 'Required',
      },
      {
        'name': 'name',
        'displayName': 'Full Name',
        'dataType': 'string',
        'required': 'YES',
        'unique': 'NO',
        'defaultType': 'N/A',
        'defaultValue': 'N/A',
        'description': 'Complete name of the customer',
        'helperText': 'Enter full name',
        'enumValues': 'N/A',
        'validation': 'Required',
      },
    ];
  }

  /// Helper method to get relationships data for display
  List<List<String>> _getRelationshipsData(ObjectCreationModel? objectData) {
    if (objectData?.relationships == null ||
        objectData!.relationships!.isEmpty) {
      return []; // Return empty list if no relationships
    }

    return objectData.relationships!.map((relationship) {
      return [
        relationship.relatedEntity ?? 'Unknown Entity',
        relationship.relationshipType ?? 'Unknown Type',
        relationship.foreignKey ?? 'N/A',
        relationship.description ?? 'No description',
        'Active', // Default status
      ];
    }).toList();
  }

  /// Helper method to get business rules data for display
  List<List<String>> _getBusinessRulesData(ObjectCreationModel? objectData) {
    if (objectData?.businessRules == null ||
        objectData!.businessRules!.isEmpty) {
      return []; // Return empty list if no business rules
    }

    return objectData.businessRules!.map((rule) {
      return [
        rule.attributeName ?? 'Unknown Attribute',
        rule.operator ?? 'Unknown Operator',
        rule.pattern ?? 'No pattern',
        rule.errorMessage ?? 'No error message',
        'Active', // Default status
      ];
    }).toList();
  }

  /// Helper method to convert API relationships to table data format
  List<Map<String, String>> _getRelationshipTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.relationships == null ||
        objectData!.relationships!.isEmpty) {
      return []; // Return empty list if no relationships
    }

    return objectData.relationships!.map((relationship) {
      return {
        'relatedEntity': relationship.relatedEntity ?? 'Unknown Entity',
        'relationshipType': relationship.relationshipType ?? 'Unknown Type',
        'foreignKey': relationship.foreignKey ?? 'N/A',
        'description': relationship.description ?? 'No description',
        'status': 'Active', // Default status
      };
    }).toList();
  }

  /// Helper method to convert API business rules to table data format
  List<Map<String, String>> _getBusinessRuleTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.businessRules == null ||
        objectData!.businessRules!.isEmpty) {
      return []; // Return empty list if no business rules
    }

    return objectData.businessRules!.map((rule) {
      return {
        'attributeName': rule.attributeName ?? 'Unknown Attribute',
        'operator': rule.operator ?? 'Unknown Operator',
        'value': rule.pattern ?? 'No value',
        'errorMessage': rule.errorMessage ?? 'No error message',
        'status': 'Active', // Default status
      };
    }).toList();
  }

  /// Helper method to convert API enum values to table data format
  List<Map<String, String>> _getEnumValueTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.enumValues == null || objectData!.enumValues!.isEmpty) {
      return []; // Return empty list if no enum values
    }

    return objectData.enumValues!.map((enumValue) {
      return {
        'entityAttribute': enumValue.entityAttribute ?? 'Unknown Attribute',
        'enumName': enumValue.enumName ?? 'Unknown Enum',
        'value': enumValue.value ?? 'No value',
        'display': enumValue.display ?? 'No display',
        'description': enumValue.description ?? 'No description',
        'sortOrder': enumValue.sortOrder?.toString() ?? '0',
        'active': (enumValue.active ?? true) ? 'Yes' : 'No',
      };
    }).toList();
  }

  /// Helper method to convert API security classification to table data format
  List<Map<String, String>> _getSecurityClassificationTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.securityClassification == null ||
        objectData!.securityClassification!.isEmpty) {
      return []; // Return empty list if no security classification
    }

    return objectData.securityClassification!.map((security) {
      return {
        'entityAttribute': security.entityAttribute ?? 'Unknown Attribute',
        'classification': security.classification ?? 'Unknown',
        'piiType': security.piiType ?? 'none',
        'encryptionRequired':
            (security.encryptionRequired ?? false) ? 'Yes' : 'No',
        'encryptionType': security.encryptionType ?? 'none',
        'maskingRequired': (security.maskingRequired ?? false) ? 'Yes' : 'No',
        'maskingPattern': security.maskingPattern ?? 'N/A',
        'accessLevel': security.accessLevel ?? 'Unknown',
        'auditTrail': (security.auditTrail ?? false) ? 'Yes' : 'No',
      };
    }).toList();
  }

  /// Helper method to convert API system permissions to table data format
  List<Map<String, String>> _getSystemPermissionTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.systemPermissions == null ||
        objectData!.systemPermissions!.isEmpty) {
      return []; // Return empty list if no system permissions
    }

    return objectData.systemPermissions!.map((permission) {
      return {
        'permissionId': permission.permissionId ?? 'Unknown ID',
        'permissionName': permission.permissionName ?? 'Unknown Name',
        'permissionType': permission.permissionType ?? 'Unknown Type',
        'resourceIdentifier':
            permission.resourceIdentifier ?? 'Unknown Resource',
        'actions': permission.actions?.join(', ') ?? 'No actions',
        'description': permission.description ?? 'No description',
        'scope': permission.scope ?? 'Unknown scope',
        'naturalLanguage': permission.naturalLanguage ?? 'No description',
        'version': permission.version?.toString() ?? '1',
        'status': permission.status ?? 'active',
      };
    }).toList();
  }

  /// Helper method to convert API role system permissions to table data format
  List<Map<String, String>> _getRoleSystemPermissionTableData(
      ObjectCreationModel? objectData) {
    if (objectData?.roleSystemPermissions == null ||
        objectData!.roleSystemPermissions!.isEmpty) {
      return []; // Return empty list if no role system permissions
    }

    return objectData.roleSystemPermissions!.map((rolePermission) {
      return {
        'roleId': rolePermission.roleId ?? 'Unknown Role',
        'permissionId': rolePermission.permissionId ?? 'Unknown Permission',
        'grantedActions':
            rolePermission.grantedActions?.join(', ') ?? 'No actions',
        'rowLevelConditions':
            rolePermission.rowLevelConditions?.toString() ?? 'No conditions',
        'naturalLanguage': rolePermission.naturalLanguage ?? 'No description',
      };
    }).toList();
  }

  /// Generic method to convert any API entity section to table data format
  List<Map<String, String>> _getGenericTableData(
      String sectionName, ObjectCreationModel? objectData) {
    if (objectData == null) {
      return [];
    }

    // Use existing specific methods for known sections
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return _getAttributeTableData(objectData);
      case 'relationships':
        return _getRelationshipTableData(objectData);
      case 'business_rules':
        return _getBusinessRuleTableData(objectData);
      case 'enum_values':
        return _getEnumValueTableData(objectData);
      case 'security_classification':
        return _getSecurityClassificationTableData(objectData);
      case 'system_permissions':
        return _getSystemPermissionTableData(objectData);
      case 'role_system_permissions':
        return _getRoleSystemPermissionTableData(objectData);
      default:
        // For unknown sections, try to extract data dynamically
        return _extractUnknownSectionData(sectionName, objectData);
    }
  }

  /// Extract data for unknown sections dynamically
  List<Map<String, String>> _extractUnknownSectionData(
      String sectionName, ObjectCreationModel objectData) {
    // This is a placeholder for future unknown sections
    // In a real implementation, you could use reflection or other dynamic approaches
    return [];
  }

  /// Helper method to get table data with API data support
  List<List<String>> _getTableDataWithApiSupport(String title,
      {ObjectCreationModel? objectData}) {
    switch (title) {
      case 'Entity Relationships':
        final apiData = _getRelationshipsData(objectData);
        if (apiData.isNotEmpty) {
          return apiData;
        }
        // Fallback to static data
        return [
          ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
          ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
          ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
        ];
      case 'Attribute Business Rules':
        final apiData = _getBusinessRulesData(objectData);
        if (apiData.isNotEmpty) {
          return apiData;
        }
        // Fallback to static data
        return [
          ['email_validation', 'email', 'format = email', 'reject', 'High'],
          ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
          ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
        ];
      case 'Enumerated Values':
        // Convert Map data to List format for display
        final apiMapData = _getEnumValueTableData(objectData);
        if (apiMapData.isNotEmpty) {
          return apiMapData
              .map((item) => [
                    item['entityAttribute'] ?? '',
                    item['enumName'] ?? '',
                    item['value'] ?? '',
                    item['display'] ?? '',
                    item['description'] ?? '',
                    item['sortOrder'] ?? '',
                    item['active'] ?? '',
                  ])
              .toList();
        }
        // Fallback to static data
        return [
          [
            'Entity.status',
            'Status Options',
            'ACTIVE',
            'Active',
            'Active status',
            '1',
            'Yes'
          ],
          [
            'Entity.status',
            'Status Options',
            'INACTIVE',
            'Inactive',
            'Inactive status',
            '2',
            'Yes'
          ],
        ];
      case 'Security Classification':
        // Convert Map data to List format for display
        final apiMapData = _getSecurityClassificationTableData(objectData);
        if (apiMapData.isNotEmpty) {
          return apiMapData
              .map((item) => [
                    item['entityAttribute'] ?? '',
                    item['classification'] ?? '',
                    item['piiType'] ?? '',
                    item['encryptionRequired'] ?? '',
                    item['encryptionType'] ?? '',
                    item['maskingRequired'] ?? '',
                    item['maskingPattern'] ?? '',
                    item['accessLevel'] ?? '',
                    item['auditTrail'] ?? '',
                  ])
              .toList();
        }
        // Fallback to static data
        return [
          [
            'Entity.id',
            'internal',
            'none',
            'No',
            'none',
            'No',
            '***',
            'read_internal',
            'Yes'
          ],
          [
            'Entity.email',
            'confidential',
            'email',
            'Yes',
            'aes256',
            'Yes',
            '***@***.***',
            'read_restricted',
            'Yes'
          ],
        ];
      case 'System Permissions':
        // Convert Map data to List format for display
        final apiMapData = _getSystemPermissionTableData(objectData);
        if (apiMapData.isNotEmpty) {
          return apiMapData
              .map((item) => [
                    item['permissionId'] ?? '',
                    item['permissionName'] ?? '',
                    item['permissionType'] ?? '',
                    item['resourceIdentifier'] ?? '',
                    item['actions'] ?? '',
                    item['description'] ?? '',
                    item['scope'] ?? '',
                    item['naturalLanguage'] ?? '',
                    item['version'] ?? '',
                    item['status'] ?? '',
                  ])
              .toList();
        }
        // Fallback to static data
        return [
          [
            'PERM_ENTITY_CONTACT',
            'Contact Entity Access',
            'entity',
            'Contact',
            'create, read, update, delete',
            'Full access to Contact entity',
            'tenant_records',
            'Permission to manage Contact records',
            '1',
            'active'
          ],
        ];
      case 'Role System Permissions':
        // Convert Map data to List format for display
        final apiMapData = _getRoleSystemPermissionTableData(objectData);
        if (apiMapData.isNotEmpty) {
          return apiMapData
              .map((item) => [
                    item['roleId'] ?? '',
                    item['permissionId'] ?? '',
                    item['grantedActions'] ?? '',
                    item['rowLevelConditions'] ?? '',
                    item['naturalLanguage'] ?? '',
                  ])
              .toList();
        }
        // Fallback to static data
        return [
          [
            'ROLE_ADMIN',
            'PERM_ENTITY_CONTACT',
            'create, read, update, delete',
            'access_level: tenant_records',
            'Admin has full access to Contact records'
          ],
        ];
      default:
        // For other sections, use the original method
        return _getTableData(title);
    }
  }

  /// Convert Map data to List format for table display
  List<List<String>> _convertMapDataToListFormat(
      List<Map<String, String>> mapData, List<String> headers) {
    return mapData.map((item) {
      return headers.map((header) {
        final key = _getKeyFromHeader(header);
        return item[key] ?? '';
      }).toList();
    }).toList();
  }

  /// Convert header back to key format
  String _getKeyFromHeader(String header) {
    // Create reverse mapping from header to key
    final headerToKeyMap = _getHeaderToKeyMapping();
    return headerToKeyMap[header] ?? header.toLowerCase();
  }

  /// Get comprehensive header to key mapping
  Map<String, String> _getHeaderToKeyMapping() {
    return {
      // Attributes headers
      'NAME': 'name',
      'DISPLAYNAME': 'displayName',
      'DATATYPE': 'dataType',
      'REQUIRED': 'required',
      'UNIQUE': 'unique',
      'DEFAULTTYPE': 'defaultType',
      'DEFAULTVALUE': 'defaultValue',
      'DESCRIPTION': 'description',
      'HELPERTEXT': 'helperText',
      'ENUMVALUES': 'enumValues',
      'VALIDATION': 'validation',

      // Relationships headers
      'RELATEDENTITY': 'relatedEntity',
      'RELATIONSHIPTYPE': 'relationshipType',
      'FOREIGNKEY': 'foreignKey',
      'STATUS': 'status',

      // Business Rules headers
      'ATTRIBUTENAME': 'attributeName',
      'OPERATOR': 'operator',
      'VALUE': 'value',
      'ERRORMESSAGE': 'errorMessage',

      // Enum Values headers
      'ENTITYATTRIBUTE': 'entityAttribute',
      'ENUMNAME': 'enumName',
      'DISPLAY': 'display',
      'SORTORDER': 'sortOrder',
      'ACTIVE': 'active',

      // Security Classification headers
      'CLASSIFICATION': 'classification',
      'PIITYPE': 'piiType',
      'ENCRYPTIONREQUIRED': 'encryptionRequired',
      'ENCRYPTIONTYPE': 'encryptionType',
      'MASKINGREQUIRED': 'maskingRequired',
      'MASKINGPATTERN': 'maskingPattern',
      'ACCESSLEVEL': 'accessLevel',
      'AUDITTRAIL': 'auditTrail',

      // System Permissions headers
      'PERMISSIONID': 'permissionId',
      'PERMISSIONNAME': 'permissionName',
      'PERMISSIONTYPE': 'permissionType',
      'RESOURCEIDENTIFIER': 'resourceIdentifier',
      'ACTIONS': 'actions',
      'SCOPE': 'scope',
      'NATURALLANGUAGE': 'naturalLanguage',
      'VERSION': 'version',

      // Role System Permissions headers
      'ROLEID': 'roleId',
      'GRANTEDACTIONS': 'grantedActions',
      'ROWLEVELCONDITIONS': 'rowLevelConditions',
    };
  }

  /// Helper method to get dynamic column headers from API data
  List<String> _getDynamicAttributeHeaders(ObjectCreationModel? objectData) {
    final sampleData = _getAttributeTableData(objectData);
    if (sampleData.isEmpty) {
      return [
        'NAME',
        'DISPLAYNAME',
        'DATATYPE',
        'REQUIRED',
        'UNIQUE',
        'DEFAULTTYPE',
        'DEFAULTVALUE',
        'DESCRIPTION',
        'HELPERTEXT',
        'ENUMVALUES',
        'VALIDATION'
      ];
    }

    // Get keys from the first data entry and convert to uppercase
    // Ensure consistent order for better UX
    final keyOrder = [
      'name',
      'displayName',
      'dataType',
      'required',
      'unique',
      'defaultType',
      'defaultValue',
      'description',
      'helperText',
      'enumValues',
      'validation'
    ];
    final availableKeys = sampleData.first.keys.toSet();

    // Return keys in preferred order, then any additional keys
    final orderedKeys = <String>[];
    for (final key in keyOrder) {
      if (availableKeys.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    // Add any remaining keys not in the preferred order
    for (final key in availableKeys) {
      if (!keyOrder.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    return orderedKeys;
  }

  /// Generic method to get dynamic headers for any entity section
  List<String> _getDynamicHeadersForSection(
      String sectionName, ObjectCreationModel? objectData) {
    final sampleData = _getGenericTableData(sectionName, objectData);
    if (sampleData.isEmpty) {
      return _getFallbackHeaders(sectionName);
    }

    // Get keys from the first data entry and convert to uppercase
    final keyOrder = _getPreferredKeyOrder(sectionName);
    final availableKeys = sampleData.first.keys.toSet();

    final orderedKeys = <String>[];

    // Add keys in preferred order first
    for (final key in keyOrder) {
      if (availableKeys.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    // Add any remaining keys not in the preferred order
    for (final key in availableKeys) {
      if (!keyOrder.contains(key)) {
        orderedKeys.add(key.toUpperCase());
      }
    }

    return orderedKeys;
  }

  /// Get preferred key order for different entity sections
  List<String> _getPreferredKeyOrder(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return [
          'name',
          'displayName',
          'dataType',
          'required',
          'unique',
          'defaultType',
          'defaultValue',
          'description',
          'helperText',
          'enumValues',
          'validation'
        ];
      case 'relationships':
        return [
          'relatedEntity',
          'relationshipType',
          'foreignKey',
          'description',
          'status'
        ];
      case 'business_rules':
        return ['attributeName', 'operator', 'value', 'errorMessage', 'status'];
      case 'enum_values':
        return [
          'entityAttribute',
          'enumName',
          'value',
          'display',
          'description',
          'sortOrder',
          'active'
        ];
      case 'security_classification':
        return [
          'entityAttribute',
          'classification',
          'piiType',
          'encryptionRequired',
          'encryptionType',
          'maskingRequired',
          'maskingPattern',
          'accessLevel',
          'auditTrail'
        ];
      case 'system_permissions':
        return [
          'permissionId',
          'permissionName',
          'permissionType',
          'resourceIdentifier',
          'actions',
          'description',
          'scope',
          'naturalLanguage',
          'version',
          'status'
        ];
      case 'role_system_permissions':
        return [
          'roleId',
          'permissionId',
          'grantedActions',
          'rowLevelConditions',
          'naturalLanguage'
        ];
      default:
        return [];
    }
  }

  /// Get fallback headers when no data is available
  List<String> _getFallbackHeaders(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'attributes':
        return [
          'NAME',
          'DISPLAYNAME',
          'DATATYPE',
          'REQUIRED',
          'UNIQUE',
          'DEFAULTTYPE',
          'DEFAULTVALUE',
          'DESCRIPTION',
          'HELPERTEXT',
          'ENUMVALUES',
          'VALIDATION'
        ];
      case 'relationships':
        return [
          'RELATEDENTITY',
          'RELATIONSHIPTYPE',
          'FOREIGNKEY',
          'DESCRIPTION',
          'STATUS'
        ];
      case 'business_rules':
        return ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS'];
      case 'enum_values':
        return [
          'ENTITYATTRIBUTE',
          'ENUMNAME',
          'VALUE',
          'DISPLAY',
          'DESCRIPTION',
          'SORTORDER',
          'ACTIVE'
        ];
      case 'security_classification':
        return [
          'ENTITYATTRIBUTE',
          'CLASSIFICATION',
          'PIITYPE',
          'ENCRYPTIONREQUIRED',
          'ENCRYPTIONTYPE',
          'MASKINGREQUIRED',
          'MASKINGPATTERN',
          'ACCESSLEVEL',
          'AUDITTRAIL'
        ];
      case 'system_permissions':
        return [
          'PERMISSIONID',
          'PERMISSIONNAME',
          'PERMISSIONTYPE',
          'RESOURCEIDENTIFIER',
          'ACTIONS',
          'DESCRIPTION',
          'SCOPE',
          'NATURALLANGUAGE',
          'VERSION',
          'STATUS'
        ];
      case 'role_system_permissions':
        return [
          'ROLEID',
          'PERMISSIONID',
          'GRANTEDACTIONS',
          'ROWLEVELCONDITIONS',
          'NATURALLANGUAGE'
        ];
      default:
        return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
    }
  }

  /// Calculate dynamic column width based on actual text measurement
  double _calculateDynamicColumnWidth(String headerText, String sectionName,
      {List<String>? sampleData}) {
    // Create TextPainter to measure text dimensions with more generous styling
    final textPainter = TextPainter(
      text: TextSpan(
        text: headerText,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          fontFamily: 'Inter',
          letterSpacing: 0.5, // Add letter spacing for better measurement
        ),
      ),
      textDirection: TextDirection.ltr,
      maxLines: 1, // Ensure single line measurement
    );

    textPainter.layout();
    double headerWidth = textPainter.size.width;

    // Debug logging for troubleshooting
    print('🔍 Header: "$headerText" - Measured width: ${headerWidth}px');

    // Consider sample data content if provided
    double contentWidth = headerWidth;
    if (sampleData != null && sampleData.isNotEmpty) {
      double maxContentWidth = 0;
      for (String content in sampleData.take(3)) {
        // Check first 3 samples
        final contentPainter = TextPainter(
          text: TextSpan(
            text: content,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'Inter',
            ),
          ),
          textDirection: TextDirection.ltr,
          maxLines: 1,
        );
        contentPainter.layout();
        maxContentWidth = math.max(maxContentWidth, contentPainter.size.width);
      }
      contentWidth = math.max(headerWidth, maxContentWidth);
    }

    // Add more generous padding for headers (50px total - 25px each side)
    // This accounts for table cell padding, borders, and any additional styling
    double calculatedWidth = contentWidth + 50;

    // Apply min/max constraints based on header type
    final constraints = _getColumnConstraints(headerText, sectionName);

    // For very long headers, be more generous with the minimum width
    double finalWidth = math.max(
        constraints.minWidth, math.min(constraints.maxWidth, calculatedWidth));

    // Special handling for extremely long headers (>15 characters)
    if (headerText.length > 15) {
      finalWidth = math.max(finalWidth,
          headerText.length * 8.5); // Rough character width estimation
    }

    print('🔍 Final width for "$headerText": ${finalWidth}px');
    return finalWidth;
  }

  /// Get column constraints based on header type and section
  ColumnConstraints _getColumnConstraints(
      String headerText, String sectionName) {
    // Default constraints
    double minWidth = 100.0; // Increased default minimum
    double maxWidth = 350.0; // Increased default maximum

    // Special handling for Security Classification section
    if (sectionName == 'security_classification') {
      // Security headers tend to be very long, so be more generous
      minWidth = 120.0;
      maxWidth = 500.0; // Much higher max for security headers

      // Specific security header adjustments
      if (headerText.contains('ENCRYPTION') || headerText.contains('MASKING')) {
        minWidth = 150.0;
        maxWidth = 600.0;
      }
    }

    // Adjust based on header type
    if (headerText.contains('DESCRIPTION') ||
        headerText.contains('NATURALLANGUAGE')) {
      minWidth = math.max(minWidth, 150.0);
      maxWidth = math.max(maxWidth, 400.0);
    } else if (headerText.contains('ID') ||
        headerText.contains('VERSION') ||
        headerText.contains('ACTIVE')) {
      minWidth = 60.0;
      maxWidth = 120.0;
    } else if (headerText.contains('REQUIRED') ||
        headerText.contains('UNIQUE') ||
        headerText.contains('ENCRYPTION') ||
        headerText.contains('MASKING')) {
      minWidth = math.max(minWidth, 120.0); // Increased for these types
      maxWidth = math.max(maxWidth, 200.0);
    } else if (headerText.contains('ACTIONS') ||
        headerText.contains('CONDITIONS')) {
      minWidth = math.max(minWidth, 120.0);
      maxWidth = math.max(maxWidth, 250.0);
    }

    // Additional safety for very long headers
    if (headerText.length > 20) {
      minWidth = math.max(minWidth, headerText.length * 7.0);
      maxWidth = math.max(maxWidth, headerText.length * 10.0);
    }

    return ColumnConstraints(minWidth: minWidth, maxWidth: maxWidth);
  }

  /// Get dynamic column widths for any section with intelligent distribution
  List<double> _getDynamicColumnWidthsForSection(
      String sectionName, List<String> headers,
      {double? availableWidth, List<Map<String, String>>? sampleData}) {
    // Calculate individual column widths
    List<double> calculatedWidths = [];

    for (int i = 0; i < headers.length; i++) {
      String header = headers[i];
      List<String>? columnSampleData;

      // Extract sample data for this column if available
      if (sampleData != null && sampleData.isNotEmpty) {
        final key = _getKeyFromHeader(header);
        columnSampleData = sampleData
            .map((row) => row[key] ?? '')
            .where((value) => value.isNotEmpty)
            .take(3)
            .toList();
      }

      double width = _calculateDynamicColumnWidth(header, sectionName,
          sampleData: columnSampleData);
      calculatedWidths.add(width);
    }

    // Apply intelligent distribution if total width exceeds available space
    if (availableWidth != null) {
      double totalCalculatedWidth = calculatedWidths.reduce((a, b) => a + b);

      print(
          '🔍 Total calculated width: ${totalCalculatedWidth}px, Available: ${availableWidth}px');

      if (totalCalculatedWidth > availableWidth) {
        // Use a more intelligent scaling approach
        // First, try to preserve the widest columns (likely the most important)
        // and scale down smaller columns more aggressively

        List<double> minWidths = headers
            .map(
                (header) => _getColumnConstraints(header, sectionName).minWidth)
            .toList();

        double totalMinWidth = minWidths.reduce((a, b) => a + b);

        if (totalMinWidth <= availableWidth) {
          // We can fit all columns at their minimum widths
          double remainingWidth = availableWidth - totalMinWidth;
          double totalExtraWidth = totalCalculatedWidth - totalMinWidth;

          if (totalExtraWidth > 0) {
            // Distribute remaining width proportionally to extra width
            for (int i = 0; i < calculatedWidths.length; i++) {
              double extraWidth = calculatedWidths[i] - minWidths[i];
              double proportionalExtra =
                  (extraWidth / totalExtraWidth) * remainingWidth;
              calculatedWidths[i] = minWidths[i] + proportionalExtra;
            }
          } else {
            // Just use minimum widths
            calculatedWidths = minWidths;
          }
        } else {
          // Even minimum widths don't fit, use simple proportional scaling
          double scaleFactor = availableWidth / totalCalculatedWidth;
          for (int i = 0; i < calculatedWidths.length; i++) {
            calculatedWidths[i] = calculatedWidths[i] * scaleFactor;
          }
        }

        print(
            '🔍 After scaling: ${calculatedWidths.map((w) => w.toStringAsFixed(1)).join(', ')}');
      }
    }

    return calculatedWidths;
  }

  /// Fallback method for backward compatibility
  Map<String, double> _getDynamicColumnWidths() {
    // This method is kept for backward compatibility
    // New implementations should use _getDynamicColumnWidthsForSection
    return {
      'NAME': 150.0,
      'DISPLAYNAME': 200.0,
      'DATATYPE': 120.0,
      'REQUIRED': 100.0,
      'UNIQUE': 100.0,
      'DEFAULTTYPE': 150.0,
      'DEFAULTVALUE': 150.0,
      'DESCRIPTION': 250.0,
      'HELPERTEXT': 200.0,
      'ENUMVALUES': 200.0,
      'VALIDATION': 120.0,
      'RELATEDENTITY': 180.0,
      'RELATIONSHIPTYPE': 150.0,
      'FOREIGNKEY': 120.0,
      'STATUS': 100.0,
      'ATTRIBUTENAME': 150.0,
      'OPERATOR': 120.0,
      'VALUE': 150.0,
      'ERRORMESSAGE': 200.0,
      'ENTITYATTRIBUTE': 150.0,
      'ENUMNAME': 120.0,
      'DISPLAY': 120.0,
      'SORTORDER': 80.0,
      'ACTIVE': 80.0,
      'CLASSIFICATION': 120.0,
      'PIITYPE': 100.0,
      'ENCRYPTIONREQUIRED': 120.0,
      'ENCRYPTIONTYPE': 120.0,
      'MASKINGREQUIRED': 120.0,
      'MASKINGPATTERN': 120.0,
      'ACCESSLEVEL': 120.0,
      'AUDITTRAIL': 100.0,
      'PERMISSIONID': 150.0,
      'PERMISSIONNAME': 150.0,
      'PERMISSIONTYPE': 120.0,
      'RESOURCEIDENTIFIER': 150.0,
      'ACTIONS': 150.0,
      'SCOPE': 120.0,
      'NATURALLANGUAGE': 180.0,
      'VERSION': 80.0,
      'ROLEID': 120.0,
      'GRANTEDACTIONS': 200.0,
      'ROWLEVELCONDITIONS': 200.0,
    };
  }

  /// Helper method to build dynamic header row
  Widget _buildDynamicHeaderRow(
      BuildContext context, ObjectCreationModel? objectData) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    return Row(
      children: headers.map((header) {
        final width = widths[header] ?? 150.0;
        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Helper method to build dynamic attribute row
  Widget _buildDynamicAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    // Use the generic header to key mapping
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? 'N/A';

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<WebHomeProviderStatic, ObjectCreationProvider>(
      builder: (context, provider, objectCreationProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(context, provider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 8),

        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 8),

        // ai label on the right
        Text(
          'AI',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    return _buildExtractedDetailsTab(context);

    // if (provider.isAIMode) {
    //   return _buildObjectsTab(context);
    // } else {
    //   return _buildExtractedDetailsTab(context);
    // }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return Consumer<ObjectCreationProvider>(
      builder: (context, objectCreationProvider, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(6.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Show loading indicator while fetching API data
              if (objectCreationProvider.isLoadingExtraction ||
                  objectCreationProvider.isLoadingJobEntities)
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          'Loading objects...',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Show error message if API call failed
              if (objectCreationProvider.error != null)
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading objects',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.red[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          objectCreationProvider.error!,
                          textAlign: TextAlign.center,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadApiData,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0058FF),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            'Retry',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // Show API objects if available
              if (!objectCreationProvider.isLoadingExtraction &&
                  !objectCreationProvider.isLoadingJobEntities &&
                  objectCreationProvider.error == null &&
                  objectCreationProvider.hasObjects)
                ...objectCreationProvider.objects
                    .map((object) => _buildObjectExpansionPanel(context,
                        'Object: ${object.displayName ?? object.name ?? 'Unknown Object'}',
                        objectData: object))
                    .toList(),

              // Show fallback objects if no API data
              if (!objectCreationProvider.isLoadingExtraction &&
                  !objectCreationProvider.isLoadingJobEntities &&
                  objectCreationProvider.error == null &&
                  !objectCreationProvider.hasObjects) ...[
                _buildObjectExpansionPanel(context, 'Object: Customer'),
                _buildObjectExpansionPanel(context, 'Object: Product'),
                _buildObjectExpansionPanel(context, 'Object: Order'),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return const ManualProcessingComponent();
  }

  Widget _buildObjectExpansionPanel(BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final isExpanded = provider.isObjectExpanded(objectTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
            // border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            // borderRadius: BorderRadius.circular(4),
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true, // slightly tighter by default
              child: ExpansionTile(
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(objectTitle, expanded),
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        objectTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    HoverBellIcon(
                      onTap: () {
                        // Bell icon click action - can be customized as needed
                      },
                    ),
                  ],
                ),
                children: [
                  _buildObjectDetailsSection(context, objectTitle,
                      objectData: objectData),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Books accordion style - recreated from global_library_accordion_flow
  Widget _buildBooksAccordionItem(AccordionItem item) {
    final isExpanded = _accordionController.isPanelExpanded(item.id);

    // Get status and count based on item title
    final statusInfo = _getStatusInfo(item.title);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
            color:
                isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
            width: isExpanded ? 2 : 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Title row
          InkWell(
            onTap: () {
              _accordionController.togglePanel(item.id);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: Row(
                children: [
                  // Title
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.title,
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.titleMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Status badge positioned on the right
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: statusInfo['backgroundColor'],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            statusInfo['status'],
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: statusInfo['textColor'],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Count
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      statusInfo['count'],
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expandable content
          if (isExpanded) ...[
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Show subtitle as content if available
                  if (item.subtitle != null) ...[
                    Container(
                      padding: EdgeInsets.all(16),
                      child: Text(
                        item.subtitle!,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyInter,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ] else ...[
                    // Show children items if no subtitle
                    ...item.children.map(
                      (child) => Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            Container(
                              width: 4,
                              height: 4,
                              decoration: BoxDecoration(
                                color: Colors.grey[400],
                                shape: BoxShape.circle,
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              child,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w400,
                                fontFamily: FontManager.fontFamilyInter,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  // Show nested accordion items
                  _buildNestedAccordionItems(context),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildObjectDetailsSection(BuildContext context, String objectTitle,
      {ObjectCreationModel? objectData}) {
    // Extract data from API response or use defaults
    final attributesCount = objectData?.attributes?.length ?? 0;
    final relationshipsCount = objectData?.relationships?.length ?? 0;
    final businessRulesCount = objectData?.businessRules?.length ?? 0;
    final enumValuesCount = objectData?.enumValues?.length ?? 0;
    final systemPermissionsCount = objectData?.systemPermissions?.length ?? 0;
    final securityClassificationCount =
        objectData?.securityClassification?.length ?? 0;

    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 4, 0),
      child: Column(
        children: [
          // Object Details accordion
          _buildSimpleAccordionItem(
            context,
            'Object Details',
            'Partial Completion',
            '3 Entity Detected',
            const Color(0xFFFEF3C7),
            const Color(0xFF92400E),
            false,
            objectData: objectData,
          ),

          // Attributes Details accordion with table
          _buildSimpleAccordionItem(
            context,
            'Attributes Details',
            _getStatusFromCount(attributesCount),
            '$attributesCount Attributes',
            _getBackgroundColorFromCount(attributesCount),
            _getTextColorFromCount(attributesCount),
            true,
            objectData: objectData,
          ),

          // Other accordion items
          _buildSimpleAccordionItem(
            context,
            'Entity Relationships',
            _getStatusFromCount(relationshipsCount),
            '$relationshipsCount rules Configured',
            _getBackgroundColorFromCount(relationshipsCount),
            _getTextColorFromCount(relationshipsCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Attribute Business Rules',
            _getStatusFromCount(businessRulesCount),
            '$businessRulesCount Configure',
            _getBackgroundColorFromCount(businessRulesCount),
            _getTextColorFromCount(businessRulesCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Enumerated Values',
            _getStatusFromCount(enumValuesCount),
            '$enumValuesCount Configure',
            _getBackgroundColorFromCount(enumValuesCount),
            _getTextColorFromCount(enumValuesCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'System Permissions',
            _getStatusFromCount(systemPermissionsCount),
            '$systemPermissionsCount Configure',
            _getBackgroundColorFromCount(systemPermissionsCount),
            _getTextColorFromCount(systemPermissionsCount),
            false,
            objectData: objectData,
          ),
          _buildSimpleAccordionItem(
            context,
            'Security Classification',
            _getStatusFromCount(securityClassificationCount),
            '$securityClassificationCount Configure',
            _getBackgroundColorFromCount(securityClassificationCount),
            _getTextColorFromCount(securityClassificationCount),
            false,
            objectData: objectData,
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable, {
    ObjectCreationModel? objectData,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('simple_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('simple_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Allow it to take available space but don't force ellipsis
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow
                                .visible, // Allow text to show fully
                            maxLines: 2, // Allow wrapping to 2 lines if needed
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Status Badge - Give it fixed space
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Container(
                    width: 100,
                    alignment: Alignment.centerRight,
                    child: Text(
                      count,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: showAttributeTable
                  ? _buildAttributeConfigurationTable(context,
                      objectData: objectData)
                  : _buildPlaceholderContent(context, title,
                      objectData: objectData),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildNestedAccordionItems(BuildContext context,
      {ObjectCreationModel? objectData}) {
    // Extract data from API response or use defaults
    final attributesCount = objectData?.attributes?.length ?? 0;
    final relationshipsCount = objectData?.relationships?.length ?? 0;
    final businessRulesCount = objectData?.businessRules?.length ?? 0;
    final enumValuesCount = objectData?.enumValues?.length ?? 0;
    final systemPermissionsCount = objectData?.systemPermissions?.length ?? 0;
    final securityClassificationCount =
        objectData?.securityClassification?.length ?? 0;

    return Column(
      children: [
        _buildNestedAccordionItem(
          context,
          'Attributes Details',
          _getStatusFromCount(attributesCount),
          '$attributesCount Attributes',
          _getBackgroundColorFromCount(attributesCount),
          _getTextColorFromCount(attributesCount),
          true,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Entity Relationships',
          _getStatusFromCount(relationshipsCount),
          '$relationshipsCount rules Configured',
          _getBackgroundColorFromCount(relationshipsCount),
          _getTextColorFromCount(relationshipsCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Attribute Business Rules',
          _getStatusFromCount(businessRulesCount),
          '$businessRulesCount Configure',
          _getBackgroundColorFromCount(businessRulesCount),
          _getTextColorFromCount(businessRulesCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Enumerated Values',
          _getStatusFromCount(enumValuesCount),
          '$enumValuesCount Configure',
          _getBackgroundColorFromCount(enumValuesCount),
          _getTextColorFromCount(enumValuesCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'System Permissions',
          _getStatusFromCount(systemPermissionsCount),
          '$systemPermissionsCount Configure',
          _getBackgroundColorFromCount(systemPermissionsCount),
          _getTextColorFromCount(systemPermissionsCount),
          false,
          objectData: objectData,
        ),
        _buildNestedAccordionItem(
          context,
          'Security Classification',
          _getStatusFromCount(securityClassificationCount),
          '$securityClassificationCount Configure',
          _getBackgroundColorFromCount(securityClassificationCount),
          _getTextColorFromCount(securityClassificationCount),
          false,
          objectData: objectData,
        ),
      ],
    );
  }

  Widget _buildNestedAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable, {
    ObjectCreationModel? objectData,
  }) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('nested_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              child: Row(
                children: [
                  // Title and Status Badge section
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Title Text - Allow it to take available space
                        Flexible(
                          flex: 3,
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // Status Badge - Give it fixed space
                        Flexible(
                          flex: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: backgroundColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.labelSmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: textColor,
                              ),
                              overflow: TextOverflow.visible,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Count
                  Text(
                    count,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Arrow icon
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded && showAttributeTable) ...[
            Container(
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
                color: Colors.white,
              ),
              child: _buildAttributeConfigurationTable(context,
                  objectData: objectData),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAttributeConfigurationTable(BuildContext context,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add Attribute button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              IgnorePointer(
                ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
                child: Opacity(
                  opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                      ? 0.5
                      : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: () => _showAddAttributeModal(context),
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Add Attribute'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      elevation: 0,
                      textStyle: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with shared scroll controller for frozen actions column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: NotificationListener<ScrollNotification>(
              onNotification: (notification) {
                // This ensures both sections scroll together
                return false;
              },
              child: Row(
                children: [
                  // Scrollable data columns section
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Column(
                        children: [
                          // Scrollable header
                          Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFF9FAFB),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(6),
                              ),
                            ),
                            child: Row(
                              children: _getDynamicAttributeHeaders(objectData)
                                  .map((header) {
                                final width =
                                    _getDynamicColumnWidths()[header] ?? 150.0;
                                return Container(
                                  width: width,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  child: Text(
                                    header,
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.labelMedium(
                                          context),
                                      fontWeight: FontWeight.w600,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                          // Scrollable body with shared vertical controller
                          Container(
                            constraints: const BoxConstraints(maxHeight: 200),
                            child: SingleChildScrollView(
                              controller:
                                  _verticalController, // Shared controller
                              child: Column(
                                children: _getAttributeTableData(objectData)
                                    .asMap()
                                    .entries
                                    .map((entry) {
                                  int index = entry.key;
                                  Map<String, String> data = entry.value;
                                  return _buildScrollableAttributeTableRow(
                                      context, index, data, objectData);
                                }).toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Frozen Actions column
                  Container(
                    width: 100,
                    decoration: const BoxDecoration(
                      border: Border(
                        left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                      ),
                    ),
                    child: Column(
                      children: [
                        // Fixed Actions header
                        Container(
                          width: double.infinity,
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child: Text(
                            'ACTIONS',
                            textAlign: TextAlign.center,
                            style: FontManager.getCustomStyle(
                              fontSize:
                                  ResponsiveFontSizes.labelMedium(context),
                              fontWeight: FontWeight.w600,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        // Fixed Actions body with synchronized scrolling
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            controller:
                                _actionsController, // Use the actions controller
                            physics:
                                const NeverScrollableScrollPhysics(), // Prevent direct user scrolling
                            child: Column(
                              children: _getAttributeTableData(objectData)
                                  .asMap()
                                  .entries
                                  .map((entry) {
                                int index = entry.key;
                                Map<String, String> data = entry.value;
                                return _buildFixedActionsTableRow(
                                    context, index, data);
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeDisplayRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Attribute Name
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: attributeName,
                child: Text(
                  attributeName,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Display Name
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: displayName,
                child: Text(
                  displayName,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Data Type
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: dataType,
                child: Text(
                  dataType,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Required
          SizedBox(
            width: 100,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: required,
                child: Text(
                  required,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Unique
          SizedBox(
            width: 100,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Tooltip(
                message: unique,
                child: Text(
                  unique,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            ),
          ),

          // Actions
          SizedBox(
            width: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () => _showEditAttributeModal(context, index,
                      attributeName, displayName, dataType, required, unique),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, attributeName),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeDisplayRowWithFixedActions(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Scrollable data columns
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Row(
                  children: [
                    // Attribute Name
                    SizedBox(
                      width: 150,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: attributeName,
                          child: Text(
                            attributeName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Display Name
                    SizedBox(
                      width: 150,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: displayName,
                          child: Text(
                            displayName,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Data Type
                    SizedBox(
                      width: 120,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: dataType,
                          child: Text(
                            dataType,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Required
                    SizedBox(
                      width: 100,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: required,
                          child: Text(
                            required,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),

                    // Unique
                    SizedBox(
                      width: 100,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Tooltip(
                          message: unique,
                          child: Text(
                            unique,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Fixed Actions column
          Container(
            width: 80,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () => _showEditAttributeModal(context, index,
                      attributeName, displayName, dataType, required, unique),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, attributeName),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeFormRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Attribute Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: attributeName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Display Name Field
          Container(
            width: 150,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: TextFormField(
              initialValue: displayName,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: const BorderSide(color: Color(0xFF0058FF)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                isDense: true,
              ),
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          // Data Type Dropdown
          Container(
            width: 120,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: dataType,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items:
                    ['string', 'number', 'boolean', 'date'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Required Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: required,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Unique Dropdown
          Container(
            width: 100,
            alignment: Alignment.centerLeft,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            child: SizedBox(
              height: 33, // Fixed height to match TextFormField
              child: DropdownButtonFormField<String>(
                value: unique,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: const BorderSide(color: Color(0xFF0058FF)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  isDense: true,
                ),
                icon: Container(
                  alignment: Alignment.centerRight,
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 20,
                  ),
                ),
                iconSize: 20,
                items: ['YES', 'NO'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  // Handle change
                },
              ),
            ),
          ),

          // Delete Action
          Container(
            width: 80,
            alignment: Alignment.center,
            child: IconButton(
              onPressed: () {
                // Handle delete
              },
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 20,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddAttributeModal(BuildContext context) {
    // Controllers for all 11 attribute fields to match table structure exactly
    final attributeNameController = TextEditingController();
    final displayNameController = TextEditingController();
    final descriptionController = TextEditingController();
    final helperTextController = TextEditingController();
    final defaultValueController = TextEditingController();
    final enumValuesController = TextEditingController();
    
    String selectedDataType = 'string';
    String selectedRequired = 'NO';
    String selectedUnique = 'NO';
    String selectedDefaultType = 'N/A';
    String selectedValidation = 'Optional';

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                width: 534,
                constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.8),
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Modal header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Add Attribute',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                          iconSize: 24,
                          color: Colors.grey[600],
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Scrollable form fields to match all 11 table columns
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            // Row 1: Name and Display Name
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableFormField(context, 'Attribute Name', attributeNameController),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: _buildEditableFormField(context, 'Display Name', displayNameController),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Row 2: Data Type and Required
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableDropdownField(
                                    context,
                                    'Data Type',
                                    ['string', 'number', 'boolean', 'date'],
                                    selectedDataType,
                                    (value) {
                                      setState(() {
                                        selectedDataType = value!;
                                      });
                                    }
                                  ),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: _buildEditableDropdownField(
                                    context,
                                    'Required',
                                    ['NO', 'YES'],
                                    selectedRequired,
                                    (value) {
                                      setState(() {
                                        selectedRequired = value!;
                                      });
                                    }
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Row 3: Unique and Default Type
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableDropdownField(
                                    context,
                                    'Unique',
                                    ['NO', 'YES'],
                                    selectedUnique,
                                    (value) {
                                      setState(() {
                                        selectedUnique = value!;
                                      });
                                    }
                                  ),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: _buildEditableDropdownField(
                                    context,
                                    'Default Type',
                                    ['N/A', 'Static', 'Dynamic', 'Function'],
                                    selectedDefaultType,
                                    (value) {
                                      setState(() {
                                        selectedDefaultType = value!;
                                      });
                                    }
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Row 4: Default Value and Description
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableFormField(context, 'Default Value', defaultValueController),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: _buildEditableFormField(context, 'Description', descriptionController),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Row 5: Helper Text and Enum Values
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableFormField(context, 'Helper Text', helperTextController),
                                ),
                                const SizedBox(width: 24),
                                Expanded(
                                  child: _buildEditableFormField(context, 'Enum Values', enumValuesController),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),

                            // Row 6: Validation (single field)
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: _buildEditableDropdownField(
                                    context,
                                    'Validation',
                                    ['Optional', 'Required', 'Custom'],
                                    selectedValidation,
                                    (value) {
                                      setState(() {
                                        selectedValidation = value!;
                                      });
                                    }
                                  ),
                                ),
                                const SizedBox(width: 24),
                                Expanded(child: Container()), // Empty space for alignment
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 40),

                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            // Add new attribute with all 11 fields
                            if (attributeNameController.text.isNotEmpty &&
                                displayNameController.text.isNotEmpty) {
                              this.setState(() {
                                _attributeData.add({
                                  'name': attributeNameController.text,
                                  'displayName': displayNameController.text,
                                  'dataType': selectedDataType,
                                  'required': selectedRequired,
                                  'unique': selectedUnique,
                                  'defaultType': selectedDefaultType,
                                  'defaultValue': defaultValueController.text.isEmpty ? 'N/A' : defaultValueController.text,
                                  'description': descriptionController.text.isEmpty ? 'N/A' : descriptionController.text,
                                  'helperText': helperTextController.text.isEmpty ? 'N/A' : helperTextController.text,
                                  'enumValues': enumValuesController.text.isEmpty ? 'N/A' : enumValuesController.text,
                                  'validation': selectedValidation,
                                });
                              });
                              Navigator.of(context).pop();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0058FF),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Apply This',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showEditAttributeModal(
      BuildContext context,
      int index,
      String attributeName,
      String displayName,
      String dataType,
      String required,
      String unique) {
    // Controllers for form fields
    final attributeNameController = TextEditingController(text: attributeName);
    final displayNameController = TextEditingController(text: displayName);
    String selectedDataType = dataType;
    String selectedRequired = required;
    String selectedUnique = unique;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                width: 600,
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Modal header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Edit Attribute',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                          iconSize: 24,
                          color: Colors.grey[600],
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Form fields in 2x3 grid layout with pre-filled values
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left column
                        Expanded(
                          child: Column(
                            children: [
                              _buildEditableFormField(context, 'Attribute Name',
                                  attributeNameController),
                              const SizedBox(height: 20),
                              _buildEditableDropdownField(
                                  context,
                                  'Data Type',
                                  ['string', 'number', 'boolean', 'date'],
                                  selectedDataType, (value) {
                                setState(() {
                                  selectedDataType = value!;
                                });
                              }),
                              const SizedBox(height: 20),
                              _buildEditableDropdownField(context, 'Unique',
                                  ['NO', 'YES'], selectedUnique, (value) {
                                setState(() {
                                  selectedUnique = value!;
                                });
                              }),
                            ],
                          ),
                        ),
                        const SizedBox(width: 24),
                        // Right column
                        Expanded(
                          child: Column(
                            children: [
                              _buildEditableFormField(context, 'Display Name',
                                  displayNameController),
                              const SizedBox(height: 20),
                              _buildEditableDropdownField(context, 'Required',
                                  ['NO', 'YES'], selectedRequired, (value) {
                                setState(() {
                                  selectedRequired = value!;
                                });
                              }),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 40),

                    // Action buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[700],
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () {
                            // Update the attribute data
                            setState(() {
                              _attributeData[index] = {
                                'attributeName': attributeNameController.text,
                                'displayName': displayNameController.text,
                                'dataType': selectedDataType,
                                'required': selectedRequired,
                                'unique': selectedUnique,
                              };
                            });
                            Navigator.of(context).pop();
                            // Refresh the parent widget
                            this.setState(() {});
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0058FF),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0,
                          ),
                          child: Text(
                            'Update',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _deleteAttribute(BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Remove the attribute from the list
                setState(() {
                  _attributeData.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmation(
      BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Handle delete logic here
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModalFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value: options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildEditableDropdownField(BuildContext context, String label,
      List<String> options, String selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalFormFieldWithValue(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: value,
          decoration: InputDecoration(
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownFieldWithValue(BuildContext context, String label,
      List<String> options, String selectedValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlaceholderTitle(title),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              IgnorePointer(
                ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
                child: Opacity(
                  opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                      ? 0.5
                      : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: () => _showAddModal(context, title),
                    icon: const Icon(Icons.add, size: 16, color: Colors.white),
                    label: Text(
                      _getAddButtonText(title),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.labelSmall(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 6),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      alignment: Alignment.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with internal scroll and fixed last column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable table section (header + body)
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child:
                              _buildScrollableFormTableHeader(context, title),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children: _buildScrollableFormTableRows(
                                  context, title,
                                  objectData: objectData),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  // width: 120,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        child: Text(
                          'ACTIONS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children: _buildFixedActionsColumn(context, title,
                                objectData: objectData),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(BuildContext context, String title) {
    List<String> headers = _getTableHeaders(title);
    List<double> widths = _getColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        children: headers.asMap().entries.map((entry) {
          int index = entry.key;
          String header = entry.value;
          double width = widths[index];

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  List<Widget> _buildTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);
    List<double> widths = _getColumnWidths(title);

    return rowsData.map((rowData) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          children: rowData.asMap().entries.map((entry) {
            int index = entry.key;
            String data = entry.value;
            double width = widths[index];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Tooltip(
                  message: data,
                  child: Text(
                    data,
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w400,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );
    }).toList();
  }

  List<int> _getColumnFlexValues(String title) {
    switch (title) {
      case 'Object Details':
      // return [
      //   2,
      //   2,
      //   2,
      //   2,
      //   4
      // ]; // PROPERTY NAME, VALUE, TYPE, REQUIRED, DESCRIPTION
      case 'Entity Relationships':
      // return [
      //   3,
      //   3,
      //   2,
      //   2,
      //   2
      // ]; // RELATIONSHIP NAME, TARGET ENTITY, TYPE, CARDINALITY, STATUS
      case 'Attribute Business Rules':
      // return [
      //   3,
      //   2,
      //   3,
      //   2,
      //   2
      // ]; // RULE NAME, ATTRIBUTE, CONDITION, ACTION, PRIORITY
      case 'Enumerated Values':
      // return [
      //   3,
      //   4,
      //   2,
      //   3,
      //   2
      // ]; // ENUM NAME, VALUES, DEFAULT, DESCRIPTION, STATUS
      case 'System Permissions':
      // return [
      //   3,
      //   2,
      //   2,
      //   3,
      //   2
      // ]; // PERMISSION NAME, ROLE, ACCESS LEVEL, RESOURCE, STATUS
      case 'Security Classification':
      // return [
      //   3,
      //   2,
      //   3,
      //   3,
      //   2
      // ]; // CLASSIFICATION, LEVEL, ATTRIBUTES, RESTRICTIONS, STATUS
      default:
        return [3, 3, 2, 2]; // NAME, VALUE, TYPE, STATUS
    }
  }

  List<String> _getTableHeaders(String title) {
    switch (title) {
      case 'Object Details':
        return ['PROPERTY NAME', 'VALUE', 'TYPE', 'REQUIRED', 'DESCRIPTION'];
      case 'Entity Relationships':
        return [
          'RELATEDENTITY',
          'RELATIONSHIPTYPE',
          'FOREIGNKEY',
          'DESCRIPTION',
          'STATUS'
        ];
      case 'Attribute Business Rules':
        return ['ATTRIBUTENAME', 'OPERATOR', 'VALUE', 'ERRORMESSAGE', 'STATUS'];
      case 'Enumerated Values':
        return [
          'ENTITYATTRIBUTE',
          'ENUMNAME',
          'VALUE',
          'DISPLAY',
          'DESCRIPTION',
          'SORTORDER',
          'ACTIVE'
        ];
      case 'System Permissions':
        return [
          'PERMISSIONID',
          'PERMISSIONNAME',
          'PERMISSIONTYPE',
          'RESOURCEIDENTIFIER',
          'ACTIONS',
          'DESCRIPTION',
          'SCOPE',
          'NATURALLANGUAGE',
          'VERSION',
          'STATUS'
        ];
      case 'Security Classification':
        return [
          'ENTITYATTRIBUTE',
          'CLASSIFICATION',
          'PIITYPE',
          'ENCRYPTIONREQUIRED',
          'ENCRYPTIONTYPE',
          'MASKINGREQUIRED',
          'MASKINGPATTERN',
          'ACCESSLEVEL',
          'AUDITTRAIL'
        ];
      case 'Role System Permissions':
        return [
          'ROLEID',
          'PERMISSIONID',
          'GRANTEDACTIONS',
          'ROWLEVELCONDITIONS',
          'NATURALLANGUAGE'
        ];
      default:
        return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
    }
  }

  /// Helper method to get dynamic table headers with API data support
  List<String> _getTableHeadersWithApiSupport(String title,
      {ObjectCreationModel? objectData}) {
    // Map title to section name
    final sectionName = _mapTitleToSectionName(title);
    if (sectionName != null) {
      return _getDynamicHeadersForSection(sectionName, objectData);
    }

    // Fallback to static headers
    return _getTableHeaders(title);
  }

  /// Map UI title to API section name
  String? _mapTitleToSectionName(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'relationships';
      case 'Attribute Business Rules':
        return 'business_rules';
      case 'Enumerated Values':
        return 'enum_values';
      case 'System Permissions':
        return 'system_permissions';
      case 'Security Classification':
        return 'security_classification';
      case 'Role System Permissions':
        return 'role_system_permissions';
      default:
        return null;
    }
  }

  List<double> _getColumnWidths(String title) {
    // Try to use dynamic width calculation if possible
    final sectionName = _mapTitleToSectionName(title);
    if (sectionName != null) {
      final headers = _getTableHeaders(title);
      final objectData = context.read<ObjectCreationProvider>().currentObject;
      final sampleData = _getGenericTableData(sectionName, objectData);

      // Calculate available width (assuming a reasonable table width)
      final screenWidth = MediaQuery.of(context).size.width;
      final availableWidth =
          screenWidth * 0.7; // Use 70% of screen width for table

      try {
        return _getDynamicColumnWidthsForSection(
          sectionName,
          headers,
          availableWidth: availableWidth,
          sampleData: sampleData,
        );
      } catch (e) {
        // Fallback to static widths if dynamic calculation fails
        print('Dynamic width calculation failed for $title: $e');
      }
    }

    // Fallback to static widths for sections not yet migrated or on error
    switch (title) {
      case 'Object Details':
        return [150, 150, 100, 100, 245];
      case 'Entity Relationships':
        return [180, 150, 120, 200, 100];
      case 'Attribute Business Rules':
        return [150, 120, 150, 200, 100];
      case 'Enumerated Values':
        return [150, 120, 100, 120, 150, 80, 80];
      case 'System Permissions':
        return [150, 150, 120, 150, 150, 150, 120, 180, 80, 100];
      case 'Security Classification':
        return [150, 120, 100, 120, 120, 120, 120, 120, 100];
      case 'Role System Permissions':
        return [120, 150, 200, 200, 200];
      default:
        return [200, 200, 100, 100];
    }
  }

  double _getTableMinWidth(String title) {
    List<double> widths = _getColumnWidths(title);
    return widths.reduce((a, b) => a + b);
  }

  List<List<String>> _getTableData(String title) {
    switch (title) {
      case 'Object Details':
        return [
          [
            'object_name',
            'Customer',
            'string',
            'YES',
            'Primary object identifier'
          ],
          [
            'created_date',
            '2024-01-15',
            'date',
            'YES',
            'Object creation timestamp'
          ],
          ['version', '1.0', 'string', 'NO', 'Object version number'],
        ];
      case 'Entity Relationships':
        return [
          ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
          ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
          ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
        ];
      case 'Attribute Business Rules':
        return [
          ['email_validation', 'email', 'format = email', 'reject', 'High'],
          ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
          ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
        ];
      case 'Enumerated Values':
        return [
          [
            'status_enum',
            'Active, Inactive, Pending',
            'Active',
            'Customer status options',
            'Active'
          ],
          [
            'type_enum',
            'Individual, Corporate',
            'Individual',
            'Customer type classification',
            'Active'
          ],
          [
            'priority_enum',
            'Low, Medium, High',
            'Medium',
            'Priority levels',
            'Active'
          ],
        ];
      case 'System Permissions':
        return [
          ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
          ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
          [
            'delete_customer',
            'SuperAdmin',
            'Delete',
            'Customer Data',
            'Restricted'
          ],
        ];
      case 'Security Classification':
        return [
          [
            'PII_Data',
            'High',
            'email, phone, address',
            'Encryption Required',
            'Active'
          ],
          ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
          [
            'Financial_Data',
            'Critical',
            'payment_info',
            'Strict Access Control',
            'Active'
          ],
        ];
      default:
        return [
          ['sample_name', 'sample_value', 'string', 'Active'],
          ['example_item', 'example_data', 'number', 'Pending'],
        ];
    }
  }

  String _getAddButtonText(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Property';
      case 'Entity Relationships':
        return 'Add Relationship';
      case 'Attribute Business Rules':
        return 'Add Rule';
      case 'Enumerated Values':
        return 'Add Enum';
      case 'System Permissions':
        return 'Add Permission';
      case 'Security Classification':
        return 'Add Classification';
      default:
        return 'Add Item';
    }
  }

  void _showAddModal(BuildContext context, String title) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 534,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getModalTitle(title),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields based on section type
                ..._buildModalFormFields(context, title),

                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Apply This',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getModalTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Object Property';
      case 'Entity Relationships':
        return 'Add Entity Relationship';
      case 'Attribute Business Rules':
        return 'Add Business Rule';
      case 'Enumerated Values':
        return 'Add Enumerated Value';
      case 'System Permissions':
        return 'Add System Permission';
      case 'Security Classification':
        return 'Add Security Classification';
      default:
        return 'Add Configuration';
    }
  }

  List<Widget> _buildModalFormFields(BuildContext context, String title) {
    switch (title) {
      case 'Object Details':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Property Name', 'property_name')),
              const SizedBox(width: 24),
              Expanded(
                  child:
                      _buildModalFormField(context, 'Value', 'Property Value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['string', 'number', 'boolean', 'date'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Required', ['No', 'Yes'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormField(context, 'Description', 'Property description'),
        ];
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Relationship Name', 'relationship_name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(
                      context, 'Target Entity', 'Target Entity')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Cardinality', ['1:1', '1:N', 'N:M'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownField(
              context, 'Status', ['Active', 'Pending', 'Inactive']),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Name', 'Enter name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(context, 'Value', 'Enter value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['string', 'number', 'boolean'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Status', ['Active', 'Inactive'])),
            ],
          ),
        ];
    }
  }

  String _getPlaceholderTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Object Details Configuration';
      case 'Entity Relationships':
        return 'Entity Relationships Configuration';
      case 'Attribute Business Rules':
        return 'Business Rules Configuration';
      case 'Enumerated Values':
        return 'Enumerated Values Configuration';
      case 'System Permissions':
        return 'System Permissions Configuration';
      case 'Security Classification':
        return 'Security Classification Configuration';
      default:
        return '$title Configuration';
    }
  }

  String _getPlaceholderDescription(String title) {
    switch (title) {
      case 'Object Details':
        return 'Configure detailed information about the object including its properties, metadata, and general settings.';
      case 'Entity Relationships':
        return 'Define relationships between this entity and other entities in your system.';
      case 'Attribute Business Rules':
        return 'Set up business rules and validation logic for the attributes of this entity.';
      case 'Enumerated Values':
        return 'Configure predefined values and options for enumerated fields.';
      case 'System Permissions':
        return 'Define access control and permission settings for this entity.';
      case 'Security Classification':
        return 'Set security levels and classification rules for data protection.';
      default:
        return 'Configure settings and options for this section.';
    }
  }

  // Helper method to get status information based on item title
  Map<String, dynamic> _getStatusInfo(String title) {
    switch (title) {
      case 'Object Details':
        return {
          'status': 'Partial Completion',
          'count': '1 Entity Detected',
          'backgroundColor': Color(0xFFFEF3C7),
          'textColor': Color(0xFF92400E),
        };
      case 'Properties & Methods':
        return {
          'status': 'Completed',
          'count': '25 Attributes',
          'backgroundColor': Color(0xFFD1FAE5),
          'textColor': Color(0xFF065F46),
        };
      default:
        return {
          'status': 'Missing',
          'count': '0 Configure',
          'backgroundColor': Color(0xFFFEE2E2),
          'textColor': Color(0xFF991B1B),
        };
    }
  }

  double _getFormTableMinWidth(String title) {
    switch (title) {
      case 'Object Details':
        return 800; // 5 columns + actions
      case 'Entity Relationships':
        return 880; // 5 columns + actions - increased to prevent overlap
      case 'Attribute Business Rules':
        return 750; // 5 columns + actions
      case 'Enumerated Values':
        return 850; // 5 columns + actions
      case 'System Permissions':
        return 750; // 5 columns + actions
      case 'Security Classification':
        return 800; // 5 columns + actions
      default:
        return 700;
    }
  }

  Widget _buildFormTableHeader(BuildContext context, String title) {
    List<String> headers = _getFormTableHeaders(title);
    List<double> widths = _getFormColumnWidths(title);

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelSmall(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildScrollableFormTableHeader(BuildContext context, String title) {
    List<String> headers =
        _getTableHeaders(title); // Get headers without ACTIONS
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Tooltip(
              message: header, // full text displayed on hover
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildFormTableRowsWithFixedActions(
      BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;
      return _buildGenericDisplayRowWithFixedActions(
          context, index, title, rowData);
    }).toList();
  }

  Widget _buildGenericDisplayRowWithFixedActions(
      BuildContext context, int index, String title, List<String> rowData) {
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Scrollable data columns
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: Row(
                  children: rowData.asMap().entries.map((entry) {
                    int colIndex = entry.key;
                    String data = entry.value;
                    double width = widths[colIndex];

                    return SizedBox(
                      width: width,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          data,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleSmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ),
          // Fixed Actions column
          Container(
            width: 120,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () =>
                      _showEditModal(context, index, title, rowData),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showDeleteConfirmationGeneric(
                      context, index, title, rowData[0]),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildFormTableRows(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;
      return _buildGenericDisplayRow(context, index, title, rowData);
    }).toList();
  }

  Widget _buildGenericDisplayRow(
      BuildContext context, int index, String title, List<String> rowData) {
    List<double> widths = _getFormColumnWidths(title);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Build display fields based on column type
          ...rowData.asMap().entries.map((entry) {
            int colIndex = entry.key;
            String data = entry.value;
            double width = widths[colIndex];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  data,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }),

          // Actions
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: () =>
                        _showEditModal(context, index, title, rowData),
                    icon: const Icon(Icons.edit_outlined),
                    color: Colors.blue[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Edit',
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => _showDeleteConfirmationGeneric(
                        context, index, title, rowData[0]),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Delete',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFieldByType(
      BuildContext context, String title, int columnIndex, String data) {
    List<String> dropdownColumns = _getDropdownColumns(title);
    List<String> headers = _getFormTableHeaders(title);

    if (columnIndex < headers.length &&
        dropdownColumns.contains(headers[columnIndex])) {
      // Build dropdown with fixed height and 4px margin
      List<String> options = _getDropdownOptions(title, headers[columnIndex]);
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: SizedBox(
          height: 33, // Fixed height to match TextFormField
          child: DropdownButtonFormField<String>(
            value: options.contains(data) ? data : options.first,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(4),
                borderSide: const BorderSide(color: Color(0xFF0058FF)),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              isDense: true,
            ),
            icon: Container(
              alignment: Alignment.centerRight,
              child: const Icon(
                Icons.keyboard_arrow_down,
                size: 20,
              ),
            ),
            iconSize: 20,
            items: options.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              );
            }).toList(),
            onChanged: (String? newValue) {
              // Handle change
            },
          ),
        ),
      );
    } else {
      // Build text field with 4px margin
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: TextFormField(
          initialValue: data,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            isDense: true,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodySmall(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      );
    }
  }

  List<String> _getFormTableHeaders(String title) {
    List<String> baseHeaders = _getTableHeaders(title);
    return [...baseHeaders, 'ACTIONS'];
  }

  List<double> _getFormColumnWidths(String title) {
    List<double> baseWidths = _getColumnWidths(title);
    return [...baseWidths, 120]; // Add 120px for actions column
  }

  List<String> _getDropdownColumns(String title) {
    switch (title) {
      case 'Object Details':
        return ['TYPE', 'REQUIRED'];
      case 'Entity Relationships':
        return ['TYPE', 'CARDINALITY', 'STATUS'];
      case 'Attribute Business Rules':
        return ['ACTION', 'PRIORITY'];
      case 'Enumerated Values':
        return ['STATUS'];
      case 'System Permissions':
        return ['ROLE', 'ACCESS LEVEL', 'STATUS'];
      case 'Security Classification':
        return ['LEVEL', 'STATUS'];
      default:
        return ['TYPE', 'STATUS'];
    }
  }

  List<String> _getDropdownOptions(String title, String column) {
    switch (column) {
      case 'TYPE':
        if (title == 'Object Details') {
          return ['string', 'number', 'boolean', 'date'];
        } else if (title == 'Entity Relationships') {
          return ['One-to-One', 'One-to-Many', 'Many-to-Many'];
        }
        return ['string', 'number', 'boolean'];
      case 'REQUIRED':
        return ['YES', 'NO'];
      case 'CARDINALITY':
        return ['1:1', '1:N', 'N:M'];
      case 'STATUS':
        return ['Active', 'Pending', 'Inactive'];
      case 'ACTION':
        return ['reject', 'warn', 'format', 'accept'];
      case 'PRIORITY':
        return ['Low', 'Medium', 'High'];
      case 'ROLE':
        return ['User', 'Admin', 'SuperAdmin'];
      case 'ACCESS LEVEL':
        return ['Read', 'Write', 'Delete'];
      case 'LEVEL':
        return ['Low', 'Medium', 'High', 'Critical'];
      default:
        return ['Active', 'Inactive'];
    }
  }

  void _showEditModal(
      BuildContext context, int index, String title, List<String> rowData) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Edit ${_getModalTitle(title)}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields based on section type with pre-filled values
                ..._buildModalFormFieldsWithValues(context, title, rowData),

                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Update',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showDeleteConfirmationGeneric(
      BuildContext context, int index, String title, String itemName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete ${_getSingularTitle(title)}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "$itemName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Handle delete logic here
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  List<Widget> _buildModalFormFieldsWithValues(
      BuildContext context, String title, List<String> rowData) {
    switch (title) {
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Relationship Name', rowData[0])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Target Entity', rowData[1])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'],
                      rowData[2])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(context,
                      'Cardinality', ['1:1', '1:N', 'N:M'], rowData[3])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownFieldWithValue(
              context, 'Status', ['Active', 'Pending', 'Inactive'], rowData[4]),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Name', rowData[0])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', rowData.length > 1 ? rowData[1] : '')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['string', 'number', 'boolean'],
                      rowData.length > 2 ? rowData[2] : 'string')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Inactive'],
                      rowData.length > 3 ? rowData[3] : 'Active')),
            ],
          ),
        ];
    }
  }

  String _getSingularTitle(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'Relationship';
      case 'Object Details':
        return 'Property';
      case 'Attribute Business Rules':
        return 'Rule';
      case 'Enumerated Values':
        return 'Enum';
      case 'System Permissions':
        return 'Permission';
      case 'Security Classification':
        return 'Classification';
      default:
        return 'Item';
    }
  }

  List<Widget> _buildScrollableFormTableRows(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    List<List<String>> rowsData =
        _getTableDataWithApiSupport(title, objectData: objectData);
    List<double> widths = _getColumnWidths(title);

    return rowsData.map((rowData) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          children: rowData.asMap().entries.map((entry) {
            int colIndex = entry.key;
            String data = entry.value;
            double width = widths[colIndex];

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  data,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
        ),
      );
    }).toList();
  }

  List<Widget> _buildFixedActionsColumn(BuildContext context, String title,
      {ObjectCreationModel? objectData}) {
    List<List<String>> rowsData =
        _getTableDataWithApiSupport(title, objectData: objectData);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;

      return IgnorePointer(
        ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
        child: Opacity(
          opacity:
              !Provider.of<WebHomeProviderStatic>(context).isAIMode ? 0.5 : 1.0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () =>
                      _showEditModal(context, index, title, rowData),
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showDeleteConfirmationGeneric(
                      context, index, title, rowData[0]),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildAttributeScrollableRow(
    BuildContext context,
    int index,
    String attributeName,
    String displayName,
    String dataType,
    String required,
    String unique,
    String description,
    String defaultValue,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Attribute Name
          SizedBox(
            width: 200,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                attributeName,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Display Name
          SizedBox(
            width: 200,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                displayName,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Data Type
          SizedBox(
            width: 120,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                dataType,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Required
          SizedBox(
            width: 110,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                required,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Unique
          SizedBox(
            width: 110,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                unique,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Description
          SizedBox(
            width: 250,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                description,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),

          // Default Value
          SizedBox(
            width: 150,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                defaultValue,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributeFixedActionsRow(
    BuildContext context,
    int index,
    String attributeName,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              // Find the attribute data for editing
              final attributeData = _attributeData[index];
              _showEditAttributeModal(
                context,
                index,
                attributeData['attributeName']!,
                attributeData['displayName']!,
                attributeData['dataType']!,
                attributeData['required']!,
                attributeData['unique']!,
              );
            },
            icon: const Icon(Icons.edit_outlined),
            color: Colors.blue[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Edit',
          ),
          const SizedBox(width: 4),
          IconButton(
            onPressed: () => _deleteAttribute(context, index, attributeName),
            icon: const Icon(Icons.delete_outline),
            color: Colors.red[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Delete',
          ),
        ],
      ),
    );
  }

  /// Builds unified table header that includes both data columns and actions column
  Widget _buildUnifiedTableHeader(
      BuildContext context, ObjectCreationModel? objectData) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();

    return Row(
      children: [
        // Data column headers
        ...headers.map((header) {
          final width = widths[header] ?? 150.0;
          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                header,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
          );
        }).toList(),
        // Actions column header
        SizedBox(
          width: 100,
          child: Align(
            alignment: Alignment.center,
            child: Text(
              'ACTIONS',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds unified table row that includes both data and actions (no individual scrolling)
  Widget _buildUnifiedTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns (no individual scrolling)
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? 'N/A';

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          // Actions column
          SizedBox(
            width: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData['name'] ??
                          attributeData['attributeName'] ??
                          '',
                      attributeData['displayName'] ?? '',
                      attributeData['dataType'] ?? '',
                      attributeData['required'] ?? '',
                      attributeData['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds unified attribute row that includes both data columns and actions column
  Widget _buildUnifiedAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? 'N/A';

            return SizedBox(
              width: width,
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  value,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w400,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
              ),
            );
          }).toList(),
          // Actions column
          SizedBox(
            width: 100,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData['name'] ??
                          attributeData['attributeName'] ??
                          '',
                      attributeData['displayName'] ?? '',
                      attributeData['dataType'] ?? '',
                      attributeData['required'] ?? '',
                      attributeData['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds unified attribute table row that includes both data and actions (no individual scrolling)
  Widget _buildUnifiedAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? 'N/A';

            return Container(
              width: width,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }).toList(),
          // Actions column
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData['name'] ??
                          attributeData['attributeName'] ??
                          '',
                      attributeData['displayName'] ?? '',
                      attributeData['dataType'] ?? '',
                      attributeData['required'] ?? '',
                      attributeData['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds attribute table row with proper horizontal scrolling
  Widget _buildAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: [
          // Data columns
          ...headers.map((header) {
            final width = widths[header] ?? 150.0;
            final key = headerToKeyMap[header] ?? header.toLowerCase();
            final value = data[key] ?? 'N/A';

            return Container(
              width: width,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            );
          }).toList(),
          // Actions column
          Container(
            width: 100,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: const BoxDecoration(
              border: Border(
                left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData['name'] ??
                          attributeData['attributeName'] ??
                          '',
                      attributeData['displayName'] ?? '',
                      attributeData['dataType'] ?? '',
                      attributeData['required'] ?? '',
                      attributeData['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds scrollable attribute row for the new table layout
  Widget _buildScrollableAttributeRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? 'N/A';

          return Container(
            width: width,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              value,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds scrollable attribute table row for the shared scroll controller implementation
  Widget _buildScrollableAttributeTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
    ObjectCreationModel? objectData,
  ) {
    final headers = _getDynamicAttributeHeaders(objectData);
    final widths = _getDynamicColumnWidths();
    final headerToKeyMap = _getHeaderToKeyMapping();

    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: headers.map((header) {
          final width = widths[header] ?? 150.0;
          final key = headerToKeyMap[header] ?? header.toLowerCase();
          final value = data[key] ?? 'N/A';

          return Container(
            width: width,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Text(
              value,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }

  /// Builds fixed actions table row for the shared scroll controller implementation
  Widget _buildFixedActionsTableRow(
    BuildContext context,
    int index,
    Map<String, String> data,
  ) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: IgnorePointer(
          ignoring: !Provider.of<WebHomeProviderStatic>(context).isAIMode,
          child: Opacity(
            opacity: !Provider.of<WebHomeProviderStatic>(context).isAIMode
                ? 0.5
                : 1.0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    // Find the attribute data for editing
                    final attributeData = _attributeData[index];
                    _showEditAttributeModal(
                      context,
                      index,
                      attributeData['name'] ??
                          attributeData['attributeName'] ??
                          '',
                      attributeData['displayName'] ?? '',
                      attributeData['dataType'] ?? '',
                      attributeData['required'] ?? '',
                      attributeData['unique'] ?? '',
                    );
                  },
                  icon: const Icon(Icons.edit_outlined),
                  color: Colors.blue[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Edit',
                ),
                const SizedBox(width: 4),
                IconButton(
                  onPressed: () =>
                      _deleteAttribute(context, index, data['name'] ?? ''),
                  icon: const Icon(Icons.delete_outline),
                  color: Colors.red[600],
                  iconSize: 18,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: 'Delete',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Builds fixed actions row for the new table layout
  Widget _buildFixedAttributeActionsRow(
    BuildContext context,
    int index,
    Map<String, String> data,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          IconButton(
            onPressed: () {
              // Find the attribute data for editing
              final attributeData = _attributeData[index];
              _showEditAttributeModal(
                context,
                index,
                attributeData['name'] ?? attributeData['attributeName'] ?? '',
                attributeData['displayName'] ?? '',
                attributeData['dataType'] ?? '',
                attributeData['required'] ?? '',
                attributeData['unique'] ?? '',
              );
            },
            icon: const Icon(Icons.edit_outlined),
            color: Colors.blue[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Edit',
          ),
          const SizedBox(width: 4),
          IconButton(
            onPressed: () =>
                _deleteAttribute(context, index, data['name'] ?? ''),
            icon: const Icon(Icons.delete_outline),
            color: Colors.red[600],
            iconSize: 18,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            tooltip: 'Delete',
          ),
        ],
      ),
    );
  }
}

class AccordionItem {
  final String id;
  final String title;
  final String? subtitle;
  final List<String> children;

  AccordionItem({
    required this.id,
    required this.title,
    this.subtitle,
    required this.children,
  });
}

// Shared HoverBellIcon component that can be used across the application
class HoverBellIcon extends StatefulWidget {
  final VoidCallback onTap;

  const HoverBellIcon({
    super.key,
    required this.onTap,
  });

  @override
  State<HoverBellIcon> createState() => _HoverBellIconState();
}

class _HoverBellIconState extends State<HoverBellIcon> {
  bool isHovered = false;
  OverlayEntry? _overlayEntry;
  final GlobalKey _bellIconKey = GlobalKey();
  bool _isOverPopup = false;

  @override
  void dispose() {
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay() {
    if (_overlayEntry != null) return;

    final RenderBox? renderBox =
        _bellIconKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 280, // Adjust horizontal position
        top: position.dy + size.height + 8, // Position below the bell icon
        child: MouseRegion(
          onEnter: (_) {
            _isOverPopup = true;
          },
          onExit: (_) {
            _isOverPopup = false;
            // Small delay to check if we're still hovering
            Future.delayed(Duration(milliseconds: 50), () {
              if (mounted && !isHovered && !_isOverPopup) {
                _removeOverlay();
              }
            });
          },
          child: Material(
            color: Colors.transparent,
            child: _buildHoverPopup(),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleMouseExit() {
    setState(() => isHovered = false);
    // Small delay to allow moving to popup
    Future.delayed(Duration(milliseconds: 50), () {
      if (mounted && !isHovered && !_isOverPopup) {
        _removeOverlay();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() => isHovered = true);
        _showOverlay();
      },
      onExit: (_) {
        _handleMouseExit();
      },
      child: InkWell(
        onTap: widget.onTap,
        hoverColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        splashFactory: NoSplash.splashFactory,
        child: Container(
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Icon(
              key: _bellIconKey,
              Icons.notifications_outlined,
              size: 18,
              color: Color(0xffFF2019),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHoverPopup() {
    return Container(
      width: 300,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Message text
          Text(
            'This Objects is already exists in your library. You need to rename the Objects to proceed.',
            textAlign: TextAlign.center,
            style: FontManager.getCustomStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontManager.regular,
              fontFamily: FontManager.fontFamilyTiemposText,
              height: 1.4,
            ),
          ),
          SizedBox(height: 20),
          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Continue button (outlined)
              SizedBox(
                height: 32,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Handle continue action
                  },
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Colors.grey.shade400, width: 1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    backgroundColor: Colors.white,
                  ),
                  child: Text(
                    'Continue',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.black87,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              // Resolve button (filled)
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      isHovered = false;
                      _isOverPopup = false;
                    });
                    _removeOverlay();
                    // Show the Object Creation Error dialog
                    _showObjectCreationErrorDialog(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xff007AFF),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    elevation: 0,
                  ),
                  child: Text(
                    'Resolve',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.titleMedium(context),
                      color: Colors.white,
                      fontWeight: FontManager.medium,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showObjectCreationErrorDialog(BuildContext context) {
    final TextEditingController customerController =
        TextEditingController(text: 'Customer_1');

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 534,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header section with shadow
                Container(
                  padding: const EdgeInsets.fromLTRB(24, 24, 24, 16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x0F000000), // #0000000F
                        blurRadius: 12,
                        offset: Offset(0, 6),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Object Creation Error',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleLarge(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        iconSize: 18,
                        color: Colors.grey[600],
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),

                // Content section
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Purpose section
                      Text(
                        'Purpose',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w600,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        'This Objects is already exists in your library. You need to rename the Objects to proceed.',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                          height: 1.3,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Customer field label
                      Text(
                        'Customer_1',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleSmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 6),

                      // Text input field
                      Container(
                        width: double.infinity,
                        child: TextFormField(
                          controller: customerController,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide:
                                  const BorderSide(color: Color(0xFF007AFF)),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 8),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.titleSmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Footer section with shadow
                Container(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color:
                            Color(0x0F000000), // #0000000F (black, 6% opacity)
                        blurRadius: 12,
                        offset: Offset(0, -6),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: ElevatedButton(
                      onPressed: () {
                        // Handle proceed action
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF007AFF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Proceed',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.titleSmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
